import argparse

from src.data.dataset import get_dataloader
from src.config import load_config
from src.utils.model_utils import setup_model_for_inference

def evaluate_model(config, model_path=None):
    """评估模型"""
    # 获取配置
    data_config = config.get('data', {})
    training_config = config.get('training', {})
    augmentation_config = config.get('augmentation', {})

    # 设置模型和设备
    model, device = setup_model_for_inference(config, model_path)

    # 创建测试数据加载器
    test_loader = get_dataloader(
        data_config['test_csv'],
        batch_size=training_config['batch_size'],
        shuffle=False,
        num_workers=training_config['num_workers'],
        augmentation_config=augmentation_config,
        is_training=False
    )

    # 创建评估器并评估
    from src.training.evaluator import Evaluator
    evaluator = Evaluator(model, device, config)
    metrics = evaluator.evaluate(test_loader, save_predictions=True)

    return metrics


def main():
    parser = argparse.ArgumentParser(description='PLT血小板计数模型评估')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--model', type=str, default=None,
                       help='模型文件路径（可选，默认使用配置中的最佳模型）')
    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 评估模型
    evaluate_model(config, args.model)


if __name__ == '__main__':
    main()
