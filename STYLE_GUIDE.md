# PLT血小板计数项目代码风格指南

## 1. 文件头部注释

每个Python文件都应该有统一的头部注释：

```python
"""
PLT血小板计数项目 - [模块名称]
PLT Platelet Counting Project - [Module Name]

[模块描述]
[Module Description]
"""
```

## 2. 导入顺序

按照以下顺序组织导入：

1. 标准库导入
2. 第三方库导入
3. 本地应用导入

每组之间用空行分隔：

```python
# 标准库
import os
import time
from typing import Dict, List, Optional

# 第三方库
import torch
import numpy as np
import pandas as pd

# 本地导入
from src.config import load_config
from src.utils import calculate_metrics
```

## 3. 类和函数定义

### 类定义
- 类名使用PascalCase
- 类之间用两个空行分隔
- 类内方法用一个空行分隔

```python
class ModelTrainer:
    """模型训练器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化训练器"""
        self.config = config
    
    def train(self) -> Dict[str, float]:
        """训练模型"""
        pass
```

### 函数定义
- 函数名使用snake_case
- 函数之间用两个空行分隔
- 参数和返回值都要有类型注解

```python
def load_model_weights(model: torch.nn.Module, path: str) -> Dict[str, Any]:
    """
    加载模型权重
    
    Args:
        model: PyTorch模型实例
        path: 权重文件路径
        
    Returns:
        加载信息字典
    """
    pass
```

## 4. 文档字符串

使用Google风格的文档字符串：

```python
def calculate_metrics(predictions: List[float], targets: List[float]) -> Dict[str, float]:
    """
    计算评估指标
    
    Args:
        predictions: 预测值列表
        targets: 真实值列表
        
    Returns:
        包含各种指标的字典
        
    Raises:
        ValueError: 当输入长度不匹配时
    """
    pass
```

## 5. 变量命名

- 变量名使用snake_case
- 常量使用UPPER_CASE
- 私有变量以下划线开头

```python
# 变量
model_path = "path/to/model.pth"
batch_size = 32

# 常量
DEFAULT_BATCH_SIZE = 32
MAX_EPOCHS = 100

# 私有变量
self._internal_state = {}
```

## 6. 行长度和格式

- 每行最多88个字符
- 长参数列表换行对齐
- 字典和列表适当换行

```python
# 函数参数换行
def create_model(
    model_name: str,
    pretrained: bool = True,
    num_classes: int = 1,
    dropout: float = 0.2
) -> torch.nn.Module:
    pass

# 字典换行
config = {
    'model': {
        'name': 'efficientnetv2_s',
        'pretrained': True
    },
    'training': {
        'batch_size': 32,
        'learning_rate': 0.001
    }
}
```

## 7. 注释

- 使用中英文双语注释
- 复杂逻辑要有详细注释
- TODO注释要有具体说明

```python
# 创建数据加载器 / Create data loader
train_loader = DataLoader(dataset, batch_size=32)

# TODO: 添加数据增强功能 / Add data augmentation features
```

## 8. 错误处理

- 使用具体的异常类型
- 提供有意义的错误信息

```python
try:
    model = torch.load(model_path)
except FileNotFoundError:
    raise FileNotFoundError(f"模型文件不存在: {model_path}")
except Exception as e:
    raise RuntimeError(f"加载模型失败: {e}")
```

## 9. 日志和打印

- 使用统一的打印格式
- 重要信息要有中英文说明

```python
print(f"开始训练模型 / Starting model training")
print(f"训练完成，用时 {elapsed_time:.2f}s / Training completed in {elapsed_time:.2f}s")
```
