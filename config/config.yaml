# PLT凝集块血小板计数配置文件

# 设备配置
device: "auto"                         # 计算设备 (auto/cuda/cpu/cuda:0/cuda:1等)

# 数据配置
data:
  train_csv: "datasets/train.csv"      # 训练数据CSV文件
  val_csv: "datasets/val.csv"          # 验证数据CSV文件
  test_csv: "datasets/test.csv"        # 测试数据CSV文件
  image_dir: "datasets/images/"        # 凝集块图像目录

# 模型配置
model:
  name: "efficientnetv2_s"             # 模型架构 (支持: efficientnetv2_s/m/l, efficientnet_b0/b1/b2/b3/b4)
  pretrained: true                     # 是否使用预训练权重
  pretrained_path: "models/efficientnet_v2_s-dd5fe13b.pth"  # 本地预训练权重路径（可选）
  num_classes: 1                       # 回归输出维度
  dropout: 0.2                         # Dropout率
  hidden_dim: 512                      # 回归头隐藏层维度
  use_batch_norm: false                # 是否在回归头中使用BatchNorm
  activation: "relu"                   # 激活函数 (relu/gelu/swish/leaky_relu)

# 训练配置
training:
  batch_size: 64                       # 批次大小
  num_epochs: 100                      # 训练轮数
  learning_rate: 0.001                 # 学习率
  weight_decay: 0.0001                 # 权重衰减
  optimizer: "adamW"                    # 优化器类型 (adam/adamw/sgd/rmsprop)
  momentum: 0.9                        # SGD动量（仅SGD使用）

  # 学习率调度器配置
  scheduler: "reduce_on_plateau"       # 调度器类型 (none/step/multistep/exponential/cosine/reduce_on_plateau)
  step_size: 30                        # StepLR步长
  gamma: 0.1                           # 学习率衰减因子
  milestones: [30, 60, 90]             # MultiStepLR里程碑
  T_max: 100                           # CosineAnnealingLR周期
  scheduler_mode: "min"                # ReduceLROnPlateau模式
  factor: 0.5                          # ReduceLROnPlateau衰减因子
  scheduler_patience: 5                # ReduceLROnPlateau耐心值

  # 损失函数配置
  loss_function: "mse"                 # 损失函数 (mse/mae/l1/huber/smooth_l1)
  huber_delta: 1.0                     # Huber损失的delta参数

  # 训练技巧
  use_amp: false                       # 混合精度训练
  gradient_clip_val: 0                 # 梯度裁剪值（0表示不裁剪）
  num_workers: 4                       # 数据加载工作进程数

# 数据增强配置
augmentation:
  resize: [224, 224]                   # 图像尺寸
  horizontal_flip: 0.5                 # 水平翻转概率
  vertical_flip: 0.3                   # 垂直翻转概率
  rotation: 15                         # 旋转角度范围
  brightness: 0.2                      # 亮度调整范围
  contrast: 0.2                        # 对比度调整范围

# 验证配置
validation:
  metrics: ["mae", "mse", "r2"]        # 评估指标
  save_best: true                      # 保存最佳模型
  patience: 15                         # 早停耐心值

# 输出配置
output:
  model_dir: "checkpoints/"            # 模型保存目录
  log_dir: "logs/"                     # 日志目录
  results_dir: "results/"              # 结果保存目录
