# EfficientNet回归模型增强指南

## 概述

增强的EfficientNet回归模型提供了更灵活的配置选项和更强大的功能，支持多种EfficientNet变体，可配置的回归头，以及便捷的模型管理功能。

## 支持的模型

### EfficientNetV2系列
- `efficientnetv2_s`: EfficientNetV2-Small (轻量级，推荐用于快速实验)
- `efficientnetv2_m`: EfficientNetV2-Medium (平衡性能和速度)
- `efficientnetv2_l`: EfficientNetV2-Large (最高精度，需要更多计算资源)

### EfficientNet系列
- `efficientnet_b0`: EfficientNet-B0 (最轻量级)
- `efficientnet_b1`: EfficientNet-B1
- `efficientnet_b2`: EfficientNet-B2
- `efficientnet_b3`: EfficientNet-B3
- `efficientnet_b4`: EfficientNet-B4 (较高精度)

## 主要功能

### 1. 灵活的模型配置

```python
from src.models.efficientnet_regressor import EfficientNetRegressor

# 基本用法
model = EfficientNetRegressor(
    model_name='efficientnetv2_s',    # 模型架构
    pretrained=True,                  # 使用预训练权重
    pretrained_path=None,             # 本地权重路径（可选）
    num_classes=1,                    # 输出维度
    dropout=0.2,                      # Dropout率
    hidden_dim=512,                   # 隐藏层维度
    use_batch_norm=False,             # 是否使用BatchNorm
    activation='relu'                 # 激活函数
)
```

### 2. 从配置文件创建模型

```python
from src.config import load_config

# 加载配置文件
config = load_config('config/config.yaml')

# 从配置创建模型
model = EfficientNetRegressor.create_from_config(config)
```

### 3. 模型信息查询

```python
# 列出所有支持的模型
EfficientNetRegressor.list_supported_models()

# 获取模型详细信息
model_info = model.get_model_info()
print(f"模型: {model_info['description']}")
print(f"总参数: {model_info['total_parameters']:,}")
print(f"可训练参数: {model_info['trainable_parameters']:,}")
```

### 4. 参数冻结功能

```python
# 冻结骨干网络，只训练回归头
model.freeze_backbone(freeze=True)

# 解冻所有参数
model.freeze_backbone(freeze=False)
```

### 5. 模型保存和加载

```python
# 保存模型（包含配置信息）
model.save_model('my_model.pth', include_config=True)

# 加载模型
loaded_model = EfficientNetRegressor.load_model('my_model.pth')

# 加载模型并覆盖部分配置
loaded_model = EfficientNetRegressor.load_model(
    'my_model.pth', 
    dropout=0.3,  # 覆盖原有的dropout设置
    activation='gelu'  # 覆盖原有的激活函数
)
```

## 配置文件说明

在`config/config.yaml`中的模型配置部分：

```yaml
model:
  name: "efficientnetv2_s"           # 模型架构
  pretrained: true                   # 是否使用预训练权重
  pretrained_path: "models/efficientnet_v2_s-dd5fe13b.pth"  # 本地权重路径
  num_classes: 1                     # 输出维度
  dropout: 0.2                       # Dropout率
  hidden_dim: 512                    # 隐藏层维度
  use_batch_norm: false              # 是否使用BatchNorm
  activation: "relu"                 # 激活函数
```

### 配置参数详解

- **name**: 选择EfficientNet变体，影响模型大小和性能
- **pretrained**: 是否使用ImageNet预训练权重，通常建议设为true
- **pretrained_path**: 本地预训练权重文件路径，如果存在则优先使用
- **num_classes**: 回归任务通常设为1
- **dropout**: 防止过拟合，范围0-1，建议0.1-0.3
- **hidden_dim**: 回归头隐藏层维度，影响模型容量
- **use_batch_norm**: 在回归头中是否使用批归一化
- **activation**: 激活函数类型，支持relu/gelu/swish/leaky_relu

## 使用建议

### 模型选择指南

1. **快速实验**: 使用`efficientnet_b0`或`efficientnetv2_s`
2. **平衡性能**: 使用`efficientnetv2_s`或`efficientnet_b1`
3. **最高精度**: 使用`efficientnetv2_m`或`efficientnet_b3/b4`
4. **资源受限**: 使用`efficientnet_b0`

### 参数调优建议

1. **Dropout率**:
   - 小数据集: 0.3-0.5
   - 中等数据集: 0.2-0.3
   - 大数据集: 0.1-0.2

2. **隐藏层维度**:
   - 简单任务: 256-512
   - 复杂任务: 512-1024
   - 高维输出: 1024+

3. **激活函数**:
   - 默认选择: ReLU
   - 更好性能: GELU或Swish
   - 避免梯度消失: LeakyReLU

### 训练策略

1. **迁移学习**:
   ```python
   # 先冻结骨干网络训练回归头
   model.freeze_backbone(freeze=True)
   # 训练几个epoch后解冻
   model.freeze_backbone(freeze=False)
   ```

2. **渐进式训练**:
   - 从小模型开始（如efficientnet_b0）
   - 验证效果后升级到更大模型

## 示例代码

### 完整训练示例

```python
from src.models.efficientnet_regressor import EfficientNetRegressor
from src.config import load_config
import torch

# 加载配置
config = load_config('config/config.yaml')

# 创建模型
model = EfficientNetRegressor.create_from_config(config)

# 打印模型信息
print(f"模型: {model.get_model_info()['description']}")
print(f"参数数量: {model.get_parameter_count():,}")

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = model.to(device)

# 示例推理
input_tensor = torch.randn(1, 3, 224, 224).to(device)
with torch.no_grad():
    prediction = model.predict(input_tensor)
    print(f"预测结果: {prediction.item()}")
```

### 模型比较示例

```python
# 比较不同模型的参数量
models_to_compare = ['efficientnet_b0', 'efficientnetv2_s', 'efficientnet_b3']

for model_name in models_to_compare:
    model = EfficientNetRegressor(model_name=model_name, pretrained=False)
    info = model.get_model_info()
    print(f"{info['description']}: {info['total_parameters']:,} 参数")
```

## 故障排除

### 常见问题

1. **内存不足**: 选择更小的模型或减少batch_size
2. **训练不收敛**: 检查学习率，考虑使用预训练权重
3. **过拟合**: 增加dropout率，使用数据增强
4. **欠拟合**: 增加模型容量或减少正则化

### 调试技巧

```python
# 检查模型结构
print(model)

# 检查参数是否可训练
for name, param in model.named_parameters():
    print(f"{name}: requires_grad={param.requires_grad}")

# 检查梯度
for name, param in model.named_parameters():
    if param.grad is not None:
        print(f"{name}: grad_norm={param.grad.norm().item()}")
```
