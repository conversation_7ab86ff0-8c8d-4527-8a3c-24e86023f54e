# PLT血小板计数项目 - Trainer使用指南

## 概述

新的Trainer类提供了一个完整、灵活且易于使用的训练框架，集成了现代深度学习训练的最佳实践。

## 主要特性

### 🚀 核心功能
- **完整的训练循环** - 自动化训练和验证过程
- **多种优化器支持** - Adam, AdamW, SGD, RMSprop
- **学习率调度器** - Step, MultiStep, Exponential, Cosine, ReduceLROnPlateau
- **混合精度训练** - 支持AMP加速训练
- **梯度裁剪** - 防止梯度爆炸

### 📊 监控和记录
- **TensorBoard集成** - 实时监控训练过程
- **指标计算** - 自动计算MAE, MSE, R²等回归指标
- **训练历史** - 完整记录训练过程
- **可视化报告** - 自动生成训练曲线和评估图表

### 🔄 回调系统
- **早停机制** - 防止过拟合
- **模型检查点** - 自动保存最佳模型
- **自定义回调** - 灵活扩展训练逻辑

### 💾 检查点管理
- **自动保存** - 训练过程中自动保存检查点
- **恢复训练** - 从检查点继续训练
- **完整状态** - 保存模型、优化器、调度器状态

## 快速开始

### 基本使用

```python
from src.models.efficientnet_regressor import EfficientNetRegressor
from src.training import Trainer
from src.config import load_config

# 加载配置
config = load_config('config/config.yaml')

# 创建模型
model = EfficientNetRegressor.from_config(config)

# 创建训练器
trainer = Trainer(
    model=model,
    config=config,
    train_loader=train_loader,
    val_loader=val_loader
)

# 开始训练
trainer.fit()

# 评估模型
metrics = trainer.evaluate(test_loader)

# 清理资源
trainer.close()
```

### 使用新的训练脚本

```bash
# 基本训练
python train_with_trainer.py --config config/config.yaml

# 从检查点恢复训练
python train_with_trainer.py --config config/config.yaml --resume checkpoints/best_model.pth

# 指定训练轮数
python train_with_trainer.py --config config/config.yaml --epochs 50

# 仅评估模式
python train_with_trainer.py --config config/config.yaml --eval-only
```

## 配置选项

### 训练配置

```yaml
training:
  # 基本参数
  batch_size: 64
  num_epochs: 100
  learning_rate: 0.001
  weight_decay: 0.0001
  
  # 优化器配置
  optimizer: "adam"                    # adam/adamw/sgd/rmsprop
  momentum: 0.9                        # SGD动量
  
  # 学习率调度器
  scheduler: "reduce_on_plateau"       # none/step/multistep/exponential/cosine/reduce_on_plateau
  step_size: 30                        # StepLR步长
  gamma: 0.1                           # 衰减因子
  milestones: [30, 60, 90]            # MultiStepLR里程碑
  scheduler_patience: 10               # ReduceLROnPlateau耐心值
  
  # 损失函数
  loss_function: "mse"                 # mse/mae/l1/huber/smooth_l1
  huber_delta: 1.0                     # Huber损失参数
  
  # 训练技巧
  use_amp: false                       # 混合精度训练
  gradient_clip_val: 0                 # 梯度裁剪（0=禁用）
```

### 验证配置

```yaml
validation:
  patience: 15                         # 早停耐心值
  save_best: true                      # 保存最佳模型
  metrics: ["mae", "mse", "r2"]       # 计算的指标
```

## 高级功能

### 自定义回调

```python
from src.training import TrainingCallback

class CustomCallback(TrainingCallback):
    def on_epoch_end(self, trainer, epoch, logs):
        val_loss = logs.get('val_loss', 0)
        if val_loss < 0.1:
            print("达到目标损失，停止训练")
            self.should_stop = True

# 添加到训练器
trainer.add_callback(CustomCallback())
```

### 手动检查点管理

```python
# 保存检查点
trainer.save_checkpoint('my_checkpoint.pth', epoch=50)

# 从检查点恢复
trainer.fit(resume_from_checkpoint='my_checkpoint.pth')
```

### 模型评估

```python
# 详细评估
metrics = trainer.evaluate(test_loader, save_predictions=True)

# 获取训练历史
history = trainer.history
train_losses = history['train_loss']
val_losses = history['val_loss']
```

## 输出文件

训练器会自动生成以下文件：

### 检查点文件
- `checkpoints/best_model.pth` - 最佳模型
- `checkpoints/final_model.pth` - 最终模型

### 日志和报告
- `logs/run_YYYYMMDD_HHMMSS/` - TensorBoard日志
- `results/training_history.json` - 训练历史数据
- `results/training_curves.png` - 训练曲线图
- `results/training_report.txt` - 文本报告

### 评估结果
- `results/evaluation_predictions.png` - 预测对比图
- `results/evaluation_errors.png` - 误差分布图

## 最佳实践

### 1. 训练策略

```python
# 渐进式训练：先小模型后大模型
config['model']['name'] = 'efficientnet_b0'  # 开始
# 验证效果后升级到 'efficientnetv2_s'

# 迁移学习：先冻结后解冻
model.freeze_backbone(freeze=True)
trainer.fit(num_epochs=10)  # 训练回归头
model.freeze_backbone(freeze=False)
trainer.fit(num_epochs=50)  # 微调整个模型
```

### 2. 超参数调优

```python
# 学习率查找
for lr in [0.1, 0.01, 0.001, 0.0001]:
    config['training']['learning_rate'] = lr
    trainer = Trainer(model, config, train_loader, val_loader)
    trainer.fit(num_epochs=5)
    # 观察收敛情况
```

### 3. 监控训练

```python
# 使用TensorBoard监控
# 在浏览器中打开: http://localhost:6006
# 命令行运行: tensorboard --logdir logs/
```

### 4. 调试技巧

```python
# 检查梯度
class GradientCheckCallback(TrainingCallback):
    def on_epoch_end(self, trainer, epoch, logs):
        for name, param in trainer.model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                print(f"{name}: grad_norm={grad_norm:.6f}")
```

## 故障排除

### 常见问题

1. **内存不足**
   - 减少batch_size
   - 使用梯度累积
   - 启用混合精度训练

2. **训练不收敛**
   - 检查学习率（可能过大或过小）
   - 检查数据预处理
   - 尝试不同的优化器

3. **过拟合**
   - 增加dropout
   - 使用数据增强
   - 减少模型复杂度
   - 早停

4. **欠拟合**
   - 增加模型容量
   - 减少正则化
   - 增加训练轮数

### 调试代码

```python
# 检查数据
for batch_idx, (images, targets) in enumerate(train_loader):
    print(f"Batch {batch_idx}: images.shape={images.shape}, targets.shape={targets.shape}")
    if batch_idx >= 2:  # 只检查前几个batch
        break

# 检查模型输出
model.eval()
with torch.no_grad():
    sample_input = torch.randn(1, 3, 224, 224)
    output = model(sample_input)
    print(f"Model output shape: {output.shape}")
    print(f"Model output value: {output.item()}")
```

## 示例代码

完整的使用示例请参考：
- `examples/trainer_example.py` - 基础和高级使用示例
- `train_with_trainer.py` - 完整的训练脚本

## 性能优化

### GPU优化
- 使用混合精度训练 (`use_amp: true`)
- 适当的batch_size（通常32-128）
- 多GPU训练（未来版本支持）

### 内存优化
- 梯度累积
- 检查点激活
- 数据加载优化

### 速度优化
- 预编译模型
- 数据预加载
- 异步数据传输
