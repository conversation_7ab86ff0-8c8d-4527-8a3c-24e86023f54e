augmentation:
  brightness: 0.2
  contrast: 0.2
  horizontal_flip: 0.5
  resize:
  - 224
  - 224
  rotation: 15
  vertical_flip: 0.3
data:
  image_dir: datasets/images/
  test_csv: datasets/test.csv
  train_csv: datasets/train.csv
  val_csv: datasets/val.csv
model:
  activation: relu
  dropout: 0.2
  hidden_dim: 512
  name: efficientnetv2_s
  num_classes: 1
  pretrained: true
  pretrained_path: models/efficientnet_v2_s-dd5fe13b.pth
  use_batch_norm: false
output:
  log_dir: logs/
  model_dir: checkpoints/
  results_dir: results/
training:
  T_max: 100
  batch_size: 64
  factor: 0.5
  gamma: 0.1
  gradient_clip_val: 0
  huber_delta: 1.0
  learning_rate: 0.001
  loss_function: mse
  milestones:
  - 30
  - 60
  - 90
  momentum: 0.9
  num_epochs: 100
  num_workers: 4
  optimizer: adam
  scheduler: none
  scheduler_mode: min
  scheduler_patience: 10
  step_size: 30
  use_amp: false
  weight_decay: 0.0001
validation:
  metrics:
  - mae
  - mse
  - r2
  patience: 15
  save_best: true
