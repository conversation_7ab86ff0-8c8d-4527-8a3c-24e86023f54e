# PLT血小板计数项目使用指南

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 准备数据
确保您的数据按以下结构组织：
```
datasets/
├── train.csv
├── val.csv
├── test.csv
└── images/
    ├── image1.jpg
    ├── image2.jpg
    └── ...
```

CSV文件格式：
```csv
image_path,plt_count
image1.jpg,150
image2.jpg,200
...
```

### 3. 配置设置
编辑 `config/config.yaml` 文件：
```yaml
# 设备配置
device: "auto"  # auto/cuda/cpu/cuda:0等

# 数据配置
data:
  train_csv: "datasets/train.csv"
  val_csv: "datasets/val.csv"
  test_csv: "datasets/test.csv"
  image_dir: "datasets/images/"

# 模型配置
model:
  name: "efficientnetv2_s"
  num_classes: 1
  dropout: 0.2

# 训练配置
training:
  batch_size: 32
  num_epochs: 100
  learning_rate: 0.001
  optimizer: "adam"
```

## 使用方法

### 训练模型
```bash
python train.py --config config/config.yaml
```

### 评估模型
```bash
python evaluate.py --config config/config.yaml
```

### 推理预测

#### 单张图像预测
```bash
python inference.py --image path/to/image.jpg
```

#### 批量预测
```bash
python inference.py --image path/to/images/ --batch-size 32
```

#### 指定设备和输出
```bash
python inference.py --image path/to/images/ --device cuda:0 --output results.csv
```

## 输出文件

### 训练输出
- `checkpoints/best_model.pth` - 最佳模型权重
- `logs/` - TensorBoard日志
- `results/training_history.png` - 训练历史图表

### 评估输出
- `results/evaluation_predictions.csv` - 预测结果CSV
- `results/evaluation_metrics.txt` - 评估指标
- `results/evaluation_predictions.png` - 预测结果图表
- `results/evaluation_errors.png` - 误差分布图

### 推理输出
- `results/predictions_YYYYMMDD_HHMMSS.csv` - 预测结果CSV

## CSV文件格式

### 预测结果CSV
```csv
image_path,full_path,raw_prediction,rounded_prediction
image1.jpg,/full/path/to/image1.jpg,149.8,150
image2.jpg,/full/path/to/image2.jpg,201.2,201
```

### 评估结果CSV
```csv
image_path,true_count,predicted_count,error
image1.jpg,150,149,1
image2.jpg,200,201,-1
```

## 配置选项

### 设备配置
- `auto`: 自动选择（优先GPU）
- `cpu`: 强制使用CPU
- `cuda`: 使用默认GPU
- `cuda:0`, `cuda:1`: 使用指定GPU

### 模型选项
- `efficientnetv2_s`: EfficientNetV2-S（推荐）
- `efficientnet_b0`: EfficientNet-B0

### 优化器选项
- `adam`: Adam优化器（推荐）
- `adamw`: AdamW优化器
- `sgd`: SGD优化器
- `rmsprop`: RMSprop优化器

### 损失函数选项
- `mse`: 均方误差（推荐）
- `mae`: 平均绝对误差
- `huber`: Huber损失
- `smooth_l1`: 平滑L1损失

## 常见问题

### Q: 如何更改图像输入尺寸？
A: 在配置文件的 `augmentation.resize` 中修改：
```yaml
augmentation:
  resize: [224, 224]  # [高度, 宽度]
```

### Q: 如何启用数据增强？
A: 在配置文件中添加：
```yaml
augmentation:
  horizontal_flip: 0.5
  vertical_flip: 0.5
  rotation: 15
  brightness: 0.2
  contrast: 0.2
```

### Q: 如何调整早停策略？
A: 在配置文件中修改：
```yaml
validation:
  patience: 15  # 等待轮数
```

### Q: 内存不足怎么办？
A: 减小批次大小：
```yaml
training:
  batch_size: 16  # 或更小
```

### Q: 如何使用预训练模型？
A: 在配置文件中指定：
```yaml
model:
  pretrained: true
  pretrained_path: "path/to/pretrained/model.pth"  # 可选
```

## 性能优化建议

1. **使用GPU**: 确保安装了CUDA版本的PyTorch
2. **调整批次大小**: 根据GPU内存调整batch_size
3. **数据增强**: 适当使用数据增强提高泛化能力
4. **学习率调度**: 使用学习率调度器优化训练
5. **混合精度**: 在配置中启用AMP加速训练

```yaml
training:
  use_amp: true  # 启用混合精度训练
```
