import os
import numpy as np
import pandas as pd
from PIL import Image, ImageDraw
import random
from tqdm import tqdm

def generate_plt_clump(plt_count, size=(224, 224), min_radius=5, max_radius=15):
    """
    生成单个血小板凝集块的模拟图像
    
    参数:
        plt_count: 血小板数量
        size: 图像大小
        min_radius: 最小血小板半径
        max_radius: 最大血小板半径
    """
    # 创建空白图像
    image = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(image)
    
    # 生成血小板
    plts = []
    for _ in range(plt_count):
        # 随机生成血小板位置和大小
        radius = random.uniform(min_radius, max_radius)
        x = random.uniform(radius, size[0] - radius)
        y = random.uniform(radius, size[1] - radius)
        
        # 随机生成血小板颜色（灰色调）
        color = random.randint(100, 200)
        plt_color = (color, color, color)
        
        # 绘制血小板
        draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                    fill=plt_color, outline='black')
        
        plts.append((x, y, radius))
    
    # 添加一些随机噪声
    noise = np.random.normal(0, 10, size=(size[1], size[0], 3))
    noise = np.clip(noise, -20, 20)
    image_array = np.array(image)
    image_array = np.clip(image_array + noise, 0, 255).astype(np.uint8)
    
    return Image.fromarray(image_array)

def generate_dataset(output_dir, num_samples=1000, train_ratio=0.7, val_ratio=0.15):
    """
    生成完整的模拟数据集
    
    参数:
        output_dir: 输出目录
        num_samples: 总样本数
        train_ratio: 训练集比例
        val_ratio: 验证集比例
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)
    
    # 生成数据
    data = []
    for i in tqdm(range(num_samples), desc="生成模拟数据"):
        # 随机生成血小板数量（1-50之间）
        plt_count = random.randint(1, 50)
        
        # 生成图像
        image = generate_plt_clump(plt_count)
        
        # 保存图像
        image_path = f'images/clump_{i:04d}.png'
        image.save(os.path.join(output_dir, image_path))
        
        # 记录数据
        data.append({
            'image_path': image_path,
            'plt_count': plt_count
        })
    
    # 转换为DataFrame
    df = pd.DataFrame(data)
    
    # 随机打乱数据
    df = df.sample(frac=1, random_state=42)
    
    # 分割数据集
    train_size = int(num_samples * train_ratio)
    val_size = int(num_samples * val_ratio)
    
    train_df = df[:train_size]
    val_df = df[train_size:train_size+val_size]
    test_df = df[train_size+val_size:]
    
    # 保存数据集
    train_df.to_csv(os.path.join(output_dir, 'train.csv'), index=False)
    val_df.to_csv(os.path.join(output_dir, 'val.csv'), index=False)
    test_df.to_csv(os.path.join(output_dir, 'test.csv'), index=False)
    
    print(f"数据集生成完成:")
    print(f"训练集: {len(train_df)} 样本")
    print(f"验证集: {len(val_df)} 样本")
    print(f"测试集: {len(test_df)} 样本")

if __name__ == '__main__':
    generate_dataset('data') 