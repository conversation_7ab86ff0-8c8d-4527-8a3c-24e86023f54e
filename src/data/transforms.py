"""
PLT血小板计数项目 - 数据变换模块
PLT Platelet Counting Project - Data Transforms Module

图像数据预处理和增强变换
Image data preprocessing and augmentation transforms
"""

from typing import Dict, Any, Optional

import torchvision.transforms as transforms


def create_inference_transform(augmentation_config: Optional[Dict[str, Any]] = None) -> transforms.Compose:
    """
    创建推理时使用的数据变换
    Create data transforms for inference

    Args:
        augmentation_config: 数据增强配置字典 / Data augmentation configuration

    Returns:
        torchvision.transforms.Compose对象 / torchvision.transforms.Compose object
    """
    if augmentation_config is None:
        augmentation_config = {}

    # 获取图像尺寸配置 / Get image size configuration
    resize = augmentation_config.get('resize', [224, 224])

    # 推理时只进行基本变换，不使用数据增强 / Only basic transforms for inference
    transform = transforms.Compose([
        transforms.Resize((resize[0], resize[1])),
        transforms.ToTensor(),
        transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
    ])

    return transform


def create_training_transform(augmentation_config: Optional[Dict[str, Any]] = None) -> transforms.Compose:
    """
    创建训练时使用的数据变换
    Create data transforms for training

    Args:
        augmentation_config: 数据增强配置字典 / Data augmentation configuration

    Returns:
        torchvision.transforms.Compose对象 / torchvision.transforms.Compose object
    """
    if augmentation_config is None:
        augmentation_config = {}

    # 获取配置参数 / Get configuration parameters
    resize = augmentation_config.get('resize', [224, 224])

    # 构建变换列表 / Build transform list
    transform_list = [
        transforms.Resize((resize[0], resize[1])),
    ]

    # 添加数据增强 / Add data augmentation
    horizontal_flip_prob = augmentation_config.get('horizontal_flip', 0.0)
    if horizontal_flip_prob > 0:
        transform_list.append(
            transforms.RandomHorizontalFlip(p=horizontal_flip_prob)
        )

    vertical_flip_prob = augmentation_config.get('vertical_flip', 0.0)
    if vertical_flip_prob > 0:
        transform_list.append(
            transforms.RandomVerticalFlip(p=vertical_flip_prob)
        )

    rotation_degrees = augmentation_config.get('rotation', 0)
    if rotation_degrees > 0:
        transform_list.append(
            transforms.RandomRotation(degrees=rotation_degrees)
        )

    # 亮度和对比度调整 / Brightness and contrast adjustment
    brightness = augmentation_config.get('brightness', 0.0)
    contrast = augmentation_config.get('contrast', 0.0)
    if brightness > 0 or contrast > 0:
        transform_list.append(transforms.ColorJitter(
            brightness=brightness if brightness > 0 else 0,
            contrast=contrast if contrast > 0 else 0
        ))

    # 添加标准化和转换为tensor / Add normalization and tensor conversion
    transform_list.extend([
        transforms.ToTensor(),
        transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
    ])

    return transforms.Compose(transform_list)


# 注意：验证时的变换与推理时相同，直接使用create_inference_transform
# Note: Validation transforms are the same as inference, use create_inference_transform directly
