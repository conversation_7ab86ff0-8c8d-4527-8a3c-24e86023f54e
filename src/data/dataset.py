"""
PLT血小板计数项目 - 数据集模块
PLT Platelet Counting Project - Dataset Module

自定义数据集类和数据加载器
Custom dataset class and data loaders
"""

import os
from typing import Dict, Any, Optional, Tuple, Union

import pandas as pd
import torch
from PIL import Image
from torch.utils.data import Dataset, DataLoader

from . import transforms


class PLTDataset(Dataset):
    """PLT血小板计数数据集类"""

    def __init__(
        self,
        csv_file: str,
        transform: Optional[object] = None,
        augmentation_config: Optional[Dict[str, Any]] = None,
        is_training: bool = True
    ):
        """
        初始化数据集
        Initialize dataset

        Args:
            csv_file: 包含图像路径和血小板数量的CSV文件路径 / CSV file path with image paths and platelet counts
            transform: 可选的图像变换 / Optional image transforms
            augmentation_config: 数据增强配置 / Data augmentation configuration
            is_training: 是否为训练模式 / Whether in training mode
        """
        self.data = pd.read_csv(csv_file)
        self.is_training = is_training
        self.csv_dir = os.path.dirname(os.path.abspath(csv_file))

        # 验证数据 / Validate data
        self._validate_data()

        # 设置数据变换 / Set up data transforms
        if transform:
            self.transform = transform
        elif augmentation_config:
            self.transform = self._create_transform_from_config(
                augmentation_config, is_training
            )
        else:
            # 默认变换 / Default transforms
            self.transform = self._create_default_transform()

    def _create_transform_from_config(
        self,
        config: Dict[str, Any],
        is_training: bool
    ) -> object:
        """
        根据配置创建数据变换
        Create data transforms from configuration
        """
        if is_training:
            return transforms.create_training_transform(config)
        else:
            return transforms.create_inference_transform(config)

    def _create_default_transform(self) -> object:
        """
        创建默认变换
        Create default transforms
        """
        if self.is_training:
            return transforms.create_training_transform()
        else:
            return transforms.create_inference_transform()

    def __len__(self) -> int:
        """返回数据集大小 / Return dataset size"""
        return len(self.data)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, str]:
        """
        获取数据项
        Get data item

        Args:
            idx: 数据索引 / Data index

        Returns:
            (图像张量, 血小板数量, 图像文件名) / (image tensor, platelet count, image filename)
        """
        img_relative_path = self.data.iloc[idx]['image_path']
        plt_count = self.data.iloc[idx]['plt_count']

        # 构建完整的图像路径 / Build complete image path
        img_path = os.path.join(self.csv_dir, img_relative_path)

        # 加载图像 / Load image
        image = Image.open(img_path).convert('RGB')

        # 应用变换 / Apply transforms
        if self.transform:
            image = self.transform(image)

        # 返回三元组：(图像，血小板数量，图像文件名)
        # Return tuple: (image, platelet count, image filename)
        return image, torch.tensor(plt_count, dtype=torch.float32), img_relative_path

    def _validate_data(self) -> None:
        """
        验证数据集的有效性
        Validate dataset validity
        """
        # 检查必需的列 / Check required columns
        required_columns = ['image_path', 'plt_count']
        for col in required_columns:
            if col not in self.data.columns:
                raise ValueError(f"CSV文件缺少必需的列 / Missing required column: {col}")

        # 检查数据类型和范围 / Check data types and ranges
        if not pd.api.types.is_numeric_dtype(self.data['plt_count']):
            raise ValueError("plt_count列必须是数值类型 / plt_count column must be numeric")

        # 检查是否有负值 / Check for negative values
        if (self.data['plt_count'] < 0).any():
            raise ValueError("plt_count不能包含负值 / plt_count cannot contain negative values")

        # 检查是否有缺失值 / Check for missing values
        if self.data['image_path'].isnull().any():
            raise ValueError("image_path列不能包含缺失值 / image_path column cannot contain null values")

        if self.data['plt_count'].isnull().any():
            raise ValueError("plt_count列不能包含缺失值 / plt_count column cannot contain null values")

        print(f"数据验证通过: {len(self.data)} 个样本 / Data validation passed: {len(self.data)} samples")


def get_dataloader(
    csv_file: str,
    batch_size: int = 32,
    shuffle: bool = True,
    num_workers: int = 4,
    augmentation_config: Optional[Dict[str, Any]] = None,
    is_training: bool = True
) -> DataLoader:
    """
    创建数据加载器
    Create data loader

    Args:
        csv_file: CSV文件路径 / CSV file path
        batch_size: 批次大小 / Batch size
        shuffle: 是否打乱数据 / Whether to shuffle data
        num_workers: 工作进程数 / Number of worker processes
        augmentation_config: 数据增强配置 / Data augmentation configuration
        is_training: 是否为训练模式 / Whether in training mode

    Returns:
        DataLoader对象 / DataLoader object
    """
    dataset = PLTDataset(
        csv_file,
        augmentation_config=augmentation_config,
        is_training=is_training
    )
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers
    )