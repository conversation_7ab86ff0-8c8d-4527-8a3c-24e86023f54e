"""
PLT血小板计数项目 - EfficientNet回归模型
PLT Platelet Counting Project - EfficientNet Regression Model

基于EfficientNet的血小板计数回归模型
EfficientNet-based platelet counting regression model
"""

import os
from typing import Dict, Any, Optional

import torch
import torch.nn as nn
import torchvision.models as models


class EfficientNetRegressor(nn.Module):
    """
    EfficientNet回归模型，支持多种EfficientNet变体
    EfficientNet regression model supporting multiple EfficientNet variants

    支持的模型 / Supported models:
    - efficientnetv2_s: EfficientNetV2-Small
    - efficientnet_b0: EfficientNet-B0
    """

    # 支持的模型配置 / Supported model configurations
    SUPPORTED_MODELS = {
        'efficientnetv2_s': {
            'model_func': models.efficientnet_v2_s,
            'weights': models.EfficientNet_V2_S_Weights.IMAGENET1K_V1,
            'description': 'EfficientNetV2-Small'
        },
        'efficientnet_b0': {
            'model_func': models.efficientnet_b0,
            'weights': models.EfficientNet_B0_Weights.IMAGENET1K_V1,
            'description': 'EfficientNet-B0'
        }
    }

    def __init__(
        self,
        model_name: str = 'efficientnetv2_s',
        pretrained: bool = True,
        pretrained_path: Optional[str] = None,
        num_classes: int = 1,
        dropout: float = 0.2,
        hidden_dim: int = 512,
        use_batch_norm: bool = False,
        activation: str = 'relu'
    ):
        """
        初始化EfficientNet回归模型
        Initialize EfficientNet regression model

        Args:
            model_name: EfficientNet模型名称 / EfficientNet model name
            pretrained: 是否使用预训练权重 / Whether to use pretrained weights
            pretrained_path: 本地预训练权重路径 / Local pretrained weights path
            num_classes: 输出类别数 / Number of output classes (usually 1 for regression)
            dropout: Dropout率 / Dropout rate
            hidden_dim: 隐藏层维度 / Hidden layer dimension
            use_batch_norm: 是否使用BatchNorm / Whether to use BatchNorm
            activation: 激活函数类型 / Activation function type ('relu', 'gelu', 'swish')
        """
        super(EfficientNetRegressor, self).__init__()

        # 保存配置参数 / Save configuration parameters
        self.model_name = model_name
        self.num_classes = num_classes
        self.dropout = dropout
        self.hidden_dim = hidden_dim
        self.use_batch_norm = use_batch_norm
        self.activation = activation

        # 验证模型名称 / Validate model name
        if model_name not in self.SUPPORTED_MODELS:
            supported_models = list(self.SUPPORTED_MODELS.keys())
            raise ValueError(
                f"不支持的模型 / Unsupported model: {model_name}. "
                f"支持的模型 / Supported models: {supported_models}"
            )

        # 设置预训练模型下载目录 / Set up pretrained model download directory
        self._setup_model_directory()

        # 创建骨干网络 / Create backbone network
        self.backbone = self._create_backbone(model_name, pretrained, pretrained_path)

        # 创建回归头 / Create regression head
        self._create_regression_head()

    def _setup_model_directory(self) -> None:
        """
        设置模型下载目录
        Set up model download directory
        """
        models_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'models'
        )
        os.makedirs(models_dir, exist_ok=True)
        os.environ['TORCH_HOME'] = models_dir

    def _create_backbone(
        self,
        model_name: str,
        pretrained: bool,
        pretrained_path: Optional[str]
    ) -> nn.Module:
        """
        创建骨干网络
        Create backbone network

        Args:
            model_name: 模型名称 / Model name
            pretrained: 是否使用预训练权重 / Whether to use pretrained weights
            pretrained_path: 本地预训练权重路径 / Local pretrained weights path

        Returns:
            骨干网络模型 / Backbone network model
        """
        model_config = self.SUPPORTED_MODELS[model_name]
        model_func = model_config['model_func']

        if pretrained_path and os.path.exists(pretrained_path):
            # 使用本地预训练权重 / Use local pretrained weights
            print(f"从本地加载预训练权重 / Loading local pretrained weights: {pretrained_path}")
            backbone = model_func(weights=None)
            try:
                state_dict = torch.load(pretrained_path, map_location='cpu')
                # 处理可能的键名不匹配 / Handle possible key name mismatches
                if 'state_dict' in state_dict:
                    state_dict = state_dict['state_dict']
                backbone.load_state_dict(state_dict, strict=False)
            except Exception as e:
                print(f"警告: 加载本地权重失败 / Warning: Failed to load local weights ({e})")
                print("将使用在线预训练权重 / Will use online pretrained weights")
                if pretrained:
                    backbone = model_func(weights=model_config['weights'])
                else:
                    backbone = model_func(weights=None)
        elif pretrained:
            # 使用在线预训练权重 / Use online pretrained weights
            print(f"使用在线预训练权重 / Using online pretrained weights: {model_config['weights']}")
            backbone = model_func(weights=model_config['weights'])
        else:
            # 不使用预训练权重 / No pretrained weights
            print("不使用预训练权重，随机初始化 / No pretrained weights, random initialization")
            backbone = model_func(weights=None)

        return backbone

    def _create_regression_head(self) -> None:
        """
        创建回归头
        Create regression head
        """
        # 获取特征维度
        if hasattr(self.backbone, 'classifier'):
            if isinstance(self.backbone.classifier, nn.Sequential):
                # EfficientNet的classifier通常是Sequential，最后一层是Linear
                for layer in reversed(self.backbone.classifier):
                    if isinstance(layer, nn.Linear):
                        num_features = layer.in_features
                        break
            elif isinstance(self.backbone.classifier, nn.Linear):
                num_features = self.backbone.classifier.in_features
            else:
                raise ValueError(f"无法确定 {self.model_name} 的特征维度")
        else:
            raise ValueError(f"模型 {self.model_name} 没有classifier属性")

        # 创建激活函数
        activation_layer = self._get_activation_layer()

        # 构建回归头
        layers = []

        # 第一个dropout
        if self.dropout > 0:
            layers.append(nn.Dropout(p=self.dropout))

        # 隐藏层
        layers.append(nn.Linear(num_features, self.hidden_dim))

        # 批归一化（可选）
        if self.use_batch_norm:
            layers.append(nn.BatchNorm1d(self.hidden_dim))

        # 激活函数
        layers.append(activation_layer)

        # 第二个dropout
        if self.dropout > 0:
            layers.append(nn.Dropout(p=self.dropout))

        # 输出层
        layers.append(nn.Linear(self.hidden_dim, self.num_classes))

        # 替换原有的分类器
        self.backbone.classifier = nn.Sequential(*layers)

    def _get_activation_layer(self):
        """获取激活函数层"""
        if self.activation.lower() == 'relu':
            return nn.ReLU()
        elif self.activation.lower() == 'gelu':
            return nn.GELU()
        elif self.activation.lower() == 'swish' or self.activation.lower() == 'silu':
            return nn.SiLU()
        elif self.activation.lower() == 'leaky_relu':
            return nn.LeakyReLU()
        else:
            print(f"警告: 不支持的激活函数 {self.activation}，使用ReLU")
            return nn.ReLU()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Forward propagation

        Args:
            x: 输入图像张量 / Input image tensor

        Returns:
            模型输出 / Model output
        """
        return self.backbone(x)

    def predict(self, x: torch.Tensor) -> torch.Tensor:
        """
        用于推理的方法，返回预测的血小板数量
        Inference method returning predicted platelet count

        Args:
            x: 输入图像张量 / Input image tensor

        Returns:
            预测结果（确保非负） / Prediction result (ensured non-negative)
        """
        self.eval()
        with torch.no_grad():
            pred = self(x)
            # 确保预测值为非负数 / Ensure predictions are non-negative
            return torch.clamp(pred, min=0.0)

    def get_parameter_count(self) -> int:
        """获取模型参数数量"""
        return sum(p.numel() for p in self.parameters())

    def get_trainable_parameter_count(self) -> int:
        """获取可训练参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_name': self.model_name,
            'description': self.SUPPORTED_MODELS[self.model_name]['description'],
            'num_classes': self.num_classes,
            'dropout': self.dropout,
            'hidden_dim': self.hidden_dim,
            'use_batch_norm': self.use_batch_norm,
            'activation': self.activation,
            'total_parameters': self.get_parameter_count(),
            'trainable_parameters': self.get_trainable_parameter_count()
        }



    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> 'EfficientNetRegressor':
        """
        从配置字典创建模型实例
        Create model instance from configuration dictionary

        Args:
            config: 包含模型配置的字典 / Dictionary containing model configuration

        Returns:
            EfficientNetRegressor实例 / EfficientNetRegressor instance
        """
        model_config = config.get('model', {})

        return cls(
            model_name=model_config.get('name', 'efficientnetv2_s'),
            pretrained=model_config.get('pretrained', True),
            pretrained_path=model_config.get('pretrained_path'),
            num_classes=model_config.get('num_classes', 1),
            dropout=model_config.get('dropout', 0.2),
            hidden_dim=model_config.get('hidden_dim', 512),
            use_batch_norm=model_config.get('use_batch_norm', False),
            activation=model_config.get('activation', 'relu')
        )

