"""
PLT血小板计数项目评估器模块
Evaluator module for PLT platelet counting project
"""

import os
import torch
from tqdm import tqdm
import numpy as np
from typing import Dict, Any, Optional

from ..utils.metrics import calculate_metrics, print_metrics, plot_predictions, plot_error_distribution, save_predictions_to_csv, save_metrics_to_file

class Evaluator:
    """PLT血小板计数模型评估器"""

    def __init__(self, 
                 model: torch.nn.Module,
                 device: torch.device,
                 config: Dict[str, Any]):
        """
        初始化评估器

        Args:
            model: 要评估的模型
            device: 计算设备
            config: 配置字典
        """
        self.model = model
        self.device = device
        self.config = config
        self.output_config = config.get('output', {})

    def evaluate(self, test_loader, save_predictions=True):
        """
        评估模型

        Args:
            test_loader: 测试数据加载器
            save_predictions: 是否保存预测结果

        Returns:
            包含评估指标的字典
        """
        self.model.eval()
        predictions = []
        targets = []
        filenames = []

        print("开始模型评估...")
        with torch.no_grad():
            progress_bar = tqdm(test_loader, desc='Evaluating')

            for images, batch_targets, batch_filenames in progress_bar:
                images = images.to(self.device)
                batch_targets = batch_targets.to(self.device)

                outputs = self.model.predict(images)  # 使用predict方法确保非负

                predictions.extend(outputs.cpu().numpy().flatten())
                targets.extend(batch_targets.cpu().numpy())
                filenames.extend(batch_filenames)

        # 计算指标
        metrics = calculate_metrics(predictions, targets)

        # 打印结果
        print_metrics(predictions, targets, "模型评估结果")

        if save_predictions:
            results_dir = self.output_config.get('results_dir', 'results')
            os.makedirs(results_dir, exist_ok=True)

            # 保存预测结果图表
            pred_plot_path = os.path.join(results_dir, 'evaluation_predictions.png')
            plot_predictions(predictions, targets, pred_plot_path)

            # 保存误差分布图
            error_plot_path = os.path.join(results_dir, 'evaluation_errors.png')
            plot_error_distribution(predictions, targets, error_plot_path)

            # 保存预测结果到CSV
            csv_path = os.path.join(results_dir, 'evaluation_predictions.csv')
            save_predictions_to_csv(predictions, targets, csv_path, filenames=filenames)

            # 保存评估指标到文件
            # metrics_path = os.path.join(results_dir, 'evaluation_metrics.txt')
            # save_metrics_to_file(predictions, targets, metrics_path)

            # print(f"评估结果已保存到: {results_dir}")
            # print(f"  - 预测结果CSV: {csv_path}")
            # print(f"  - 评估指标: {metrics_path}")

        return metrics
