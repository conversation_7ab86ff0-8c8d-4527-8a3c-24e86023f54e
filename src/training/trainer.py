"""
PLT血小板计数项目训练器模块
Trainer module for PLT platelet counting project
"""

import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, Any, Optional
import json

from ..utils.metrics import calculate_metrics


class Trainer:
    """PLT血小板计数模型训练器"""

    def __init__(self,
                 model: nn.Module,
                 config: Dict[str, Any],
                 train_loader,
                 val_loader,
                 device: Optional[torch.device] = None):
        """
        初始化训练器

        Args:
            model: 要训练的模型
            config: 配置字典
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            device: 计算设备
        """
        self.model = model
        self.config = config
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 移动模型到设备
        self.model = self.model.to(self.device)

        # 获取配置
        self.training_config = config.get('training', {})
        self.validation_config = config.get('validation', {})
        self.output_config = config.get('output', {})

        # 创建评估器
        from .evaluator import Evaluator
        self.evaluator = Evaluator(self.model, self.device, self.config)

        # 初始化训练组件
        self.optimizer = self._create_optimizer()
        self.criterion = self._create_loss_function()
        self.scheduler = self._create_scheduler()
        self.scaler = self._create_scaler()

        # 初始化记录
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': [],
            'val_metrics': [],
            'learning_rates': []
        }

        # 早停相关
        self.patience = self.validation_config.get('patience', 15)
        self.best_val_loss = float('inf')
        self.patience_counter = 0

        # 模型保存路径
        self.best_model_path = os.path.join(
            self.output_config.get('model_dir', 'checkpoints'),
            'best_model.pth'
        )

        # TensorBoard
        self.writer = None
        self._setup_tensorboard()

        print(f"训练器初始化完成，使用设备: {self.device}")

    def _create_optimizer(self):
        """创建优化器"""
        optimizer_type = self.training_config.get('optimizer', 'adam').lower()
        lr = self.training_config.get('learning_rate', 0.001)
        weight_decay = self.training_config.get('weight_decay', 0.0001)

        if optimizer_type == 'adam':
            return optim.Adam(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        elif optimizer_type == 'adamw':
            return optim.AdamW(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        elif optimizer_type == 'sgd':
            momentum = self.training_config.get('momentum', 0.9)
            return optim.SGD(self.model.parameters(), lr=lr, weight_decay=weight_decay, momentum=momentum)
        elif optimizer_type == 'rmsprop':
            return optim.RMSprop(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")

    def _create_loss_function(self):
        """创建损失函数"""
        loss_type = self.training_config.get('loss_function', 'mse').lower()

        if loss_type == 'mse':
            return nn.MSELoss()
        elif loss_type == 'mae' or loss_type == 'l1':
            return nn.L1Loss()
        elif loss_type == 'huber':
            delta = self.training_config.get('huber_delta', 1.0)
            return nn.HuberLoss(delta=delta)
        elif loss_type == 'smooth_l1':
            return nn.SmoothL1Loss()
        else:
            raise ValueError(f"不支持的损失函数类型: {loss_type}")

    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler_type = self.training_config.get('scheduler', 'none').lower()

        if scheduler_type == 'none' or scheduler_type is None:
            return None
        elif scheduler_type == 'step':
            step_size = self.training_config.get('step_size', 30)
            gamma = self.training_config.get('gamma', 0.1)
            return optim.lr_scheduler.StepLR(self.optimizer, step_size=step_size, gamma=gamma)
        elif scheduler_type == 'multistep':
            milestones = self.training_config.get('milestones', [30, 60, 90])
            gamma = self.training_config.get('gamma', 0.1)
            return optim.lr_scheduler.MultiStepLR(self.optimizer, milestones=milestones, gamma=gamma)
        elif scheduler_type == 'exponential':
            gamma = self.training_config.get('gamma', 0.95)
            return optim.lr_scheduler.ExponentialLR(self.optimizer, gamma=gamma)
        elif scheduler_type == 'cosine':
            T_max = self.training_config.get('T_max', self.training_config.get('num_epochs', 100))
            return optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=T_max)
        elif scheduler_type == 'reduce_on_plateau':
            mode = self.training_config.get('scheduler_mode', 'min')
            factor = self.training_config.get('factor', 0.5)
            patience = self.training_config.get('scheduler_patience', 10)
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode=mode, factor=factor, patience=patience
            )
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")

    def _create_scaler(self):
        """创建混合精度缩放器"""
        if self.training_config.get('use_amp', False) and self.device.type == 'cuda':
            return torch.cuda.amp.GradScaler()
        return None

    def _setup_tensorboard(self):
        """设置TensorBoard"""
        log_dir = os.path.join(
            self.output_config.get('log_dir', 'logs'),
            f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        self.writer = SummaryWriter(log_dir)
        print(f"TensorBoard日志目录: {log_dir}")

    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        predictions = []
        targets = []

        progress_bar = tqdm(self.train_loader, desc='Training', leave=False)

        for images, batch_targets, _ in progress_bar:
            images = images.to(self.device)
            batch_targets = batch_targets.to(self.device)

            self.optimizer.zero_grad()

            if self.scaler is not None:
                # 混合精度训练
                with torch.cuda.amp.autocast():
                    outputs = self.model(images)
                    loss = self.criterion(outputs.squeeze(), batch_targets)

                self.scaler.scale(loss).backward()

                # 梯度裁剪（可选）
                if self.training_config.get('gradient_clip_val', 0) > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(),
                        self.training_config['gradient_clip_val']
                    )

                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                # 常规训练
                outputs = self.model(images)
                loss = self.criterion(outputs.squeeze(), batch_targets)
                loss.backward()

                # 梯度裁剪（可选）
                if self.training_config.get('gradient_clip_val', 0) > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(),
                        self.training_config['gradient_clip_val']
                    )

                self.optimizer.step()

            # 记录数据
            total_loss += loss.item()
            predictions.extend(outputs.squeeze().detach().cpu().numpy())
            targets.extend(batch_targets.detach().cpu().numpy())

            # 更新进度条
            progress_bar.set_postfix({'loss': loss.item()})

        # 计算epoch指标
        avg_loss = total_loss / len(self.train_loader)
        metrics = calculate_metrics(predictions, targets)

        return avg_loss, metrics

    def validate_epoch(self):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        predictions = []
        targets = []

        with torch.no_grad():
            progress_bar = tqdm(self.val_loader, desc='Validation', leave=False)

            for images, batch_targets, _ in progress_bar:
                images = images.to(self.device)
                batch_targets = batch_targets.to(self.device)

                outputs = self.model(images)
                loss = self.criterion(outputs.squeeze(), batch_targets)

                total_loss += loss.item()
                predictions.extend(outputs.squeeze().cpu().numpy())
                targets.extend(batch_targets.cpu().numpy())

                progress_bar.set_postfix({'loss': loss.item()})

        # 计算epoch指标
        avg_loss = total_loss / len(self.val_loader)
        metrics = calculate_metrics(predictions, targets)

        return avg_loss, metrics

    def fit(self, num_epochs: Optional[int] = None, resume_from_checkpoint: Optional[str] = None):
        """
        训练模型

        Args:
            num_epochs: 训练轮数，如果为None则使用配置中的值
            resume_from_checkpoint: 从检查点恢复训练的路径
        """
        if num_epochs is None:
            num_epochs = self.training_config.get('num_epochs', 100)

        start_epoch = 0

        # 从检查点恢复
        if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
            start_epoch = self._load_checkpoint(resume_from_checkpoint)
            print(f"从检查点恢复训练，开始epoch: {start_epoch}")

        print(f"开始训练，共{num_epochs}个epoch")
        print(f"训练集大小: {len(self.train_loader.dataset)}")
        print(f"验证集大小: {len(self.val_loader.dataset)}")

        try:
            for epoch in range(start_epoch, num_epochs):
                epoch_start_time = time.time()
                print(f'\nEpoch {epoch+1}/{num_epochs}')

                # 训练
                train_loss, train_metrics = self.train_epoch()

                # 验证
                val_loss, val_metrics = self.validate_epoch()

                # 记录历史
                self.history['train_loss'].append(train_loss)
                self.history['val_loss'].append(val_loss)
                self.history['train_metrics'].append(train_metrics)
                self.history['val_metrics'].append(val_metrics)

                # 记录学习率
                current_lr = self.optimizer.param_groups[0]['lr']
                if self.scheduler is not None and hasattr(self.scheduler, 'get_last_lr'):
                    current_lr = self.scheduler.get_last_lr()[0]
                self.history['learning_rates'].append(current_lr)

                # 计算epoch时间
                epoch_time = time.time() - epoch_start_time

                # 打印结果
                print(f'Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')
                print(f'Train MAE: {train_metrics["mae"]:.4f}, Val MAE: {val_metrics["mae"]:.4f}')
                print(f'Train R²: {train_metrics["r2"]:.4f}, Val R²: {val_metrics["r2"]:.4f}')
                print(f'Learning Rate: {current_lr:.6f}, Epoch Time: {epoch_time:.2f}s')

                # 记录到TensorBoard
                self._log_to_tensorboard(epoch, train_loss, val_loss, train_metrics, val_metrics, current_lr)

                # 学习率调度
                if self.scheduler is not None:
                    if 'ReduceLROnPlateau' in str(type(self.scheduler)):
                        self.scheduler.step(val_loss)
                    else:
                        self.scheduler.step()

                # 保存最佳模型
                if val_loss < self.best_val_loss:
                    self.best_val_loss = val_loss
                    self.patience_counter = 0
                    self._save_best_model(epoch, val_loss)
                    print(f'新的最佳模型已保存，验证损失: {val_loss:.4f}')
                else:
                    self.patience_counter += 1

                # 早停检查
                if self.patience > 0 and self.patience_counter >= self.patience:
                    print(f'\n早停触发: 验证损失在{self.patience}个epoch内没有改善')
                    break

        except KeyboardInterrupt:
            print("\n训练被用户中断")

        # 生成最终报告
        self._generate_training_report()

        print("训练完成！")

    def _save_best_model(self, epoch, val_loss):
        """保存最佳模型"""
        from ..utils.model_utils import save_model_checkpoint

        metrics = {'best_val_loss': val_loss}
        save_model_checkpoint(
            self.model,
            self.best_model_path,
            epoch,
            self.optimizer.state_dict(),
            self.scheduler.state_dict() if self.scheduler else None,
            self.config,
            metrics
        )

    def _log_to_tensorboard(self, epoch, train_loss, val_loss, train_metrics, val_metrics, learning_rate):
        """记录到TensorBoard"""
        if self.writer is None:
            return

        # 记录损失
        self.writer.add_scalar('Loss/Train', train_loss, epoch)
        self.writer.add_scalar('Loss/Validation', val_loss, epoch)

        # 记录指标
        for metric_name, value in train_metrics.items():
            self.writer.add_scalar(f'Metrics/Train_{metric_name}', value, epoch)

        for metric_name, value in val_metrics.items():
            self.writer.add_scalar(f'Metrics/Val_{metric_name}', value, epoch)

        # 记录学习率
        self.writer.add_scalar('Learning_Rate', learning_rate, epoch)

        # 记录模型参数直方图（每10个epoch记录一次）
        if epoch % 10 == 0:
            for name, param in self.model.named_parameters():
                if param.requires_grad:
                    self.writer.add_histogram(f'Parameters/{name}', param, epoch)
                    if param.grad is not None:
                        self.writer.add_histogram(f'Gradients/{name}', param.grad, epoch)

    def _load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device, weights_only=False)

        # 加载模型状态
        self.model.load_state_dict(checkpoint['model_state_dict'])

        # 加载优化器状态
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # 加载调度器状态
        if self.scheduler and 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # 恢复历史记录
        if 'history' in checkpoint:
            self.history = checkpoint['history']

        return checkpoint.get('epoch', 0) + 1

    def _generate_training_report(self):
        """生成训练报告"""
        if not self.history['train_loss']:
            return

        results_dir = self.output_config.get('results_dir', 'results')
        os.makedirs(results_dir, exist_ok=True)

        # 保存训练历史
        history_path = os.path.join(results_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            # 转换numpy类型为Python原生类型以便JSON序列化
            serializable_history = {}
            for key, value in self.history.items():
                if key in ['train_metrics', 'val_metrics']:
                    serializable_history[key] = [
                        {k: float(v) for k, v in metrics.items()} for metrics in value
                    ]
                else:
                    serializable_history[key] = [float(x) for x in value]
            json.dump(serializable_history, f, indent=2)

        # 绘制训练曲线
        self._plot_training_curves()

        # 生成文本报告
        self._generate_text_report()

        print(f"训练报告已保存到: {results_dir}")

    def _plot_training_curves(self):
        """绘制训练曲线"""
        results_dir = self.output_config.get('results_dir', 'results')

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        epochs = range(1, len(self.history['train_loss']) + 1)

        # 损失曲线
        axes[0, 0].plot(epochs, self.history['train_loss'], label='Train Loss', marker='o', markersize=3)
        axes[0, 0].plot(epochs, self.history['val_loss'], label='Val Loss', marker='s', markersize=3)
        axes[0, 0].set_title('Training and Validation Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # MAE曲线
        train_mae = [m['mae'] for m in self.history['train_metrics']]
        val_mae = [m['mae'] for m in self.history['val_metrics']]
        axes[0, 1].plot(epochs, train_mae, label='Train MAE', marker='o', markersize=3)
        axes[0, 1].plot(epochs, val_mae, label='Val MAE', marker='s', markersize=3)
        axes[0, 1].set_title('Mean Absolute Error')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('MAE')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # R²曲线
        train_r2 = [m['r2'] for m in self.history['train_metrics']]
        val_r2 = [m['r2'] for m in self.history['val_metrics']]
        axes[1, 0].plot(epochs, train_r2, label='Train R²', marker='o', markersize=3)
        axes[1, 0].plot(epochs, val_r2, label='Val R²', marker='s', markersize=3)
        axes[1, 0].set_title('R² Score')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('R²')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 学习率曲线
        axes[1, 1].plot(epochs, self.history['learning_rates'], label='Learning Rate', marker='o', markersize=3)
        axes[1, 1].set_title('Learning Rate Schedule')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Learning Rate')
        axes[1, 1].set_yscale('log')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片
        curves_path = os.path.join(results_dir, 'training_curves.png')
        plt.savefig(curves_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _generate_text_report(self):
        """生成文本报告"""
        results_dir = self.output_config.get('results_dir', 'results')
        report_path = os.path.join(results_dir, 'training_report.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("PLT血小板计数模型训练报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 模型信息
            if hasattr(self.model, 'get_model_info'):
                model_info = self.model.get_model_info()
                f.write("模型信息:\n")
                for key, value in model_info.items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")

            # 训练配置
            f.write("训练配置:\n")
            for key, value in self.training_config.items():
                f.write(f"  {key}: {value}\n")
            f.write("\n")

            # 训练结果
            if self.history['train_loss']:
                best_epoch = np.argmin(self.history['val_loss'])
                f.write("训练结果:\n")
                f.write(f"  总训练轮数: {len(self.history['train_loss'])}\n")
                f.write(f"  最佳epoch: {best_epoch + 1}\n")
                f.write(f"  最佳验证损失: {self.history['val_loss'][best_epoch]:.4f}\n")
                f.write(f"  最终训练损失: {self.history['train_loss'][-1]:.4f}\n")
                f.write(f"  最终验证损失: {self.history['val_loss'][-1]:.4f}\n")

                # 最佳epoch的指标
                best_val_metrics = self.history['val_metrics'][best_epoch]
                f.write(f"\n最佳epoch验证指标:\n")
                for metric, value in best_val_metrics.items():
                    f.write(f"  {metric}: {value:.4f}\n")

    def evaluate(self, test_loader, save_predictions=True):
        """
        评估模型

        Args:
            test_loader: 测试数据加载器
            save_predictions: 是否保存预测结果

        Returns:
            包含评估指标字典
        """
        return self.evaluator.evaluate(test_loader, save_predictions)

    def close(self):
        """关闭训练器，清理资源"""
        if self.writer:
            self.writer.close()
            print("TensorBoard writer已关闭")
