"""
PLT血小板计数项目 - 配置管理器
PLT Platelet Counting Project - Configuration Manager

配置文件的加载、保存和设备配置管理
Configuration file loading, saving and device configuration management
"""

import os
from typing import Dict, Any

import torch
import yaml


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    Load configuration file

    Args:
        config_path: 配置文件路径 / Configuration file path

    Returns:
        配置字典 / Configuration dictionary

    Raises:
        FileNotFoundError: 当配置文件不存在时 / When config file doesn't exist
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在 / Config file not found: {config_path}")

    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 创建必要的目录 / Create necessary directories
    _create_directories(config)

    return config


def _create_directories(config: Dict[str, Any]) -> None:
    """
    创建配置中指定的目录
    Create directories specified in configuration
    """
    if 'output' in config:
        dirs_to_create = [
            config['output'].get('model_dir', 'checkpoints'),
            config['output'].get('log_dir', 'logs'),
            config['output'].get('results_dir', 'results')
        ]

        for dir_path in dirs_to_create:
            os.makedirs(dir_path, exist_ok=True)


def save_config(config: Dict[str, Any], save_path: str) -> None:
    """
    保存配置到文件
    Save configuration to file

    Args:
        config: 配置字典 / Configuration dictionary
        save_path: 保存路径 / Save path
    """
    with open(save_path, 'w', encoding='utf-8') as f:
        yaml.dump(
            config, f,
            default_flow_style=False,
            allow_unicode=True,
            indent=2
        )


def get_device_from_config(config: Dict[str, Any]) -> torch.device:
    """
    从配置中获取设备
    Get device from configuration

    Args:
        config: 配置字典 / Configuration dictionary

    Returns:
        torch.device对象 / torch.device object
    """
    device_str = config.get('device', 'auto')

    if device_str == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_str)

    return device
