"""
PLT血小板计数项目模型工具模块
Model utilities module for PLT platelet counting project
"""

import os
import torch
import numpy as np
import time
from typing import Dict, Any, List, Tuple

from ..models.efficientnet_regressor import EfficientNetRegressor
from ..config import get_device_from_config


def create_model_from_config(config: Dict[str, Any]) -> EfficientNetRegressor:
    """
    从配置创建模型
    
    Args:
        config: 配置字典
        
    Returns:
        创建的模型实例
    """
    model = EfficientNetRegressor.create_from_config(config)
    return model


def load_model_weights(model: torch.nn.Module, model_path: str, device: torch.device) -> Dict[str, Any]:
    """
    加载模型权重
    
    Args:
        model: 模型实例
        model_path: 模型文件路径
        device: 计算设备
        
    Returns:
        加载信息字典
    """
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        epoch = checkpoint.get('epoch', 'unknown')
        print(f"从检查点加载模型: epoch {epoch}")
        return {'epoch': epoch, 'checkpoint': checkpoint}
    else:
        model.load_state_dict(checkpoint)
        print("加载模型权重")
        return {'epoch': 'unknown', 'checkpoint': checkpoint}


def setup_model_for_inference(config: Dict[str, Any], model_path: str = None, device: str = None) -> Tuple[EfficientNetRegressor, torch.device]:
    """
    为推理设置模型
    
    Args:
        config: 配置字典
        model_path: 模型文件路径，如果为None则使用配置中的最佳模型
        device: 计算设备，如果为None则使用配置中的设备
        
    Returns:
        (模型实例, 设备)
    """
    # 设置设备
    if device:
        device_obj = torch.device(device)
    else:
        device_obj = get_device_from_config(config)
    
    print(f"使用设备: {device_obj}")
    
    # 创建模型
    model = create_model_from_config(config)
    
    # 确定模型路径
    if model_path is None:
        model_path = os.path.join(
            config.get('output', {}).get('model_dir', 'checkpoints'),
            'best_model.pth'
        )
    
    # 加载模型权重
    load_model_weights(model, model_path, device_obj)
    
    # 移动模型到设备并设置为评估模式
    model = model.to(device_obj)
    model.eval()
    
    return model, device_obj


def save_model_checkpoint(model: torch.nn.Module, save_path: str, epoch: int = None, 
                         optimizer_state: Dict = None, scheduler_state: Dict = None,
                         config: Dict[str, Any] = None, metrics: Dict[str, float] = None):
    """
    保存模型检查点
    
    Args:
        model: 模型实例
        save_path: 保存路径
        epoch: 训练轮数
        optimizer_state: 优化器状态
        scheduler_state: 调度器状态
        config: 配置字典
        metrics: 评估指标
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'epoch': epoch,
        'config': config,
        'metrics': metrics or {}
    }
    
    if optimizer_state:
        checkpoint['optimizer_state_dict'] = optimizer_state
    
    if scheduler_state:
        checkpoint['scheduler_state_dict'] = scheduler_state
    
    torch.save(checkpoint, save_path)
    print(f"模型检查点已保存到: {save_path}")


def get_model_summary(model: EfficientNetRegressor) -> Dict[str, Any]:
    """
    获取模型摘要信息
    
    Args:
        model: 模型实例
        
    Returns:
        模型摘要字典
    """
    model_info = model.get_model_info()
    
    summary = {
        'model_name': model_info.get('model_name', 'unknown'),
        'description': model_info['description'],
        'total_parameters': model_info['total_parameters'],
        'trainable_parameters': model_info['trainable_parameters'],
        'model_size_mb': model_info['total_parameters'] * 4 / 1024 / 1024,  # 假设float32
        'num_classes': model_info.get('num_classes', 1)
    }
    
    return summary


def print_model_info(model: EfficientNetRegressor):
    """
    打印模型信息
    
    Args:
        model: 模型实例
    """
    summary = get_model_summary(model)
    
    print("\n=== 模型信息 ===")
    print(f"模型类型: {summary['description']}")
    print(f"总参数数: {summary['total_parameters']:,}")
    print(f"可训练参数数: {summary['trainable_parameters']:,}")
    print(f"模型大小: {summary['model_size_mb']:.2f} MB")
    
    # 设备信息
    device = next(model.parameters()).device
    print(f"模型设备: {device}")


def benchmark_model_inference(model: torch.nn.Module, input_shape: List[int], 
                             batch_sizes: List[int] = None, num_runs: int = 50) -> Dict[str, Any]:
    """
    基准测试模型推理性能
    
    Args:
        model: 模型实例
        input_shape: 输入形状 [C, H, W]
        batch_sizes: 要测试的批次大小列表
        num_runs: 每个批次大小的测试次数
        
    Returns:
        性能测试结果
    """
    if batch_sizes is None:
        batch_sizes = [1, 4, 8, 16, 32]
    
    model.eval()
    device = next(model.parameters()).device
    
    results = {}
    
    print(f"\n=== 模型推理性能测试 ===")
    print(f"输入形状: {input_shape}")
    print(f"设备: {device}")
    print(f"测试次数: {num_runs}")
    
    for batch_size in batch_sizes:
        print(f"\n测试批次大小: {batch_size}")
        
        # 创建随机输入
        input_tensor = torch.randn(batch_size, *input_shape).to(device)
        
        # 预热
        with torch.no_grad():
            for _ in range(5):
                _ = model(input_tensor)
        
        # 同步GPU
        if device.type == 'cuda':
            torch.cuda.synchronize()
        
        # 测试推理时间
        times = []
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                _ = model(input_tensor)
                
                if device.type == 'cuda':
                    torch.cuda.synchronize()
                
                end_time = time.time()
                times.append(end_time - start_time)
        
        # 计算统计信息
        times = np.array(times)
        batch_results = {
            'batch_size': batch_size,
            'mean_time': float(np.mean(times)),
            'std_time': float(np.std(times)),
            'min_time': float(np.min(times)),
            'max_time': float(np.max(times)),
            'fps': float(batch_size / np.mean(times)),
            'throughput': float(num_runs * batch_size / np.sum(times))
        }
        
        results[f'batch_{batch_size}'] = batch_results
        
        # 打印结果
        print(f"  平均时间: {batch_results['mean_time']*1000:.2f} ms")
        print(f"  FPS: {batch_results['fps']:.2f}")
        print(f"  吞吐量: {batch_results['throughput']:.2f} images/sec")
    
    return results


def estimate_model_memory(model: torch.nn.Module, input_shape: List[int], batch_size: int = 1) -> Dict[str, float]:
    """
    估算模型内存使用量
    
    Args:
        model: 模型实例
        input_shape: 输入形状 [C, H, W]
        batch_size: 批次大小
        
    Returns:
        内存使用估算字典
    """
    # 模型参数内存
    model_params = sum(p.numel() for p in model.parameters())
    model_memory = model_params * 4  # 假设float32，4字节
    
    # 输入数据内存
    input_size = batch_size * np.prod(input_shape)
    input_memory = input_size * 4
    
    # 估算激活内存（粗略估计）
    activation_memory = input_memory * 8  # 经验值
    
    # 梯度内存（训练时）
    gradient_memory = model_memory
    
    total_memory = model_memory + input_memory + activation_memory + gradient_memory
    
    memory_stats = {
        'model_memory_mb': model_memory / 1024 / 1024,
        'input_memory_mb': input_memory / 1024 / 1024,
        'activation_memory_mb': activation_memory / 1024 / 1024,
        'gradient_memory_mb': gradient_memory / 1024 / 1024,
        'total_memory_mb': total_memory / 1024 / 1024
    }
    
    return memory_stats


def print_memory_usage(memory_stats: Dict[str, float], batch_size: int, input_shape: List[int]):
    """
    打印内存使用统计
    
    Args:
        memory_stats: 内存统计字典
        batch_size: 批次大小
        input_shape: 输入形状
    """
    print(f"\n=== 内存使用估算 ===")
    print(f"批次大小: {batch_size}")
    print(f"输入形状: {input_shape}")
    print(f"模型参数内存: {memory_stats['model_memory_mb']:.2f} MB")
    print(f"输入数据内存: {memory_stats['input_memory_mb']:.2f} MB")
    print(f"激活内存(估算): {memory_stats['activation_memory_mb']:.2f} MB")
    print(f"梯度内存(训练时): {memory_stats['gradient_memory_mb']:.2f} MB")
    print(f"总内存(估算): {memory_stats['total_memory_mb']:.2f} MB")


def compare_models(config: Dict[str, Any], input_shape: List[int] = None) -> Dict[str, Dict]:
    """
    比较不同模型的性能
    
    Args:
        config: 配置字典
        input_shape: 输入形状，如果为None则从配置中获取
        
    Returns:
        模型比较结果
    """
    if input_shape is None:
        augmentation_config = config.get('augmentation', {})
        input_size = augmentation_config.get('resize', [224, 224])
        input_shape = [3] + input_size
    
    # 支持的模型列表
    models_to_test = ['efficientnetv2_s', 'efficientnet_b0']
    
    print("="*50)
    print("模型性能对比")
    print("="*50)
    
    results = {}
    batch_size = 8  # 固定批次大小进行比较
    
    for model_name in models_to_test:
        print(f"\n测试模型: {model_name}")
        print("-" * 30)
        
        try:
            # 创建模型配置
            model_config = config.copy()
            model_config['model']['name'] = model_name
            
            # 创建模型
            model = create_model_from_config(model_config)
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = model.to(device)
            
            # 获取模型信息
            summary = get_model_summary(model)
            print_model_info(model)
            
            # 内存使用估算
            memory_stats = estimate_model_memory(model, input_shape, batch_size)
            print_memory_usage(memory_stats, batch_size, input_shape)
            
            # 性能测试
            perf_results = benchmark_model_inference(model, input_shape, [batch_size], num_runs=30)
            
            results[model_name] = {
                'summary': summary,
                'memory': memory_stats,
                'performance': perf_results[f'batch_{batch_size}']
            }
            
        except Exception as e:
            print(f"测试 {model_name} 时出错: {e}")
            results[model_name] = {'error': str(e)}
    
    # 打印对比总结
    print("\n" + "="*70)
    print("模型对比总结")
    print("="*70)
    print(f"{'模型':<20} {'参数数':<15} {'模型大小(MB)':<15} {'推理时间(ms)':<15} {'FPS':<10}")
    print("-" * 70)
    
    for model_name, result in results.items():
        if 'error' not in result:
            summary = result['summary']
            perf = result['performance']
            print(f"{model_name:<20} {summary['total_parameters']:,<15} "
                  f"{summary['model_size_mb']:<15.2f} {perf['mean_time']*1000:<15.2f} {perf['fps']:<10.2f}")
        else:
            print(f"{model_name:<20} {'错误':<15} {'错误':<15} {'错误':<15} {'错误':<10}")
    
    return results
