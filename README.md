# PLT凝集块血小板数量评估系统

这个项目使用深度学习方法来评估PLT（血小板）凝集块内部的血小板数量。系统基于EfficientNetV2模型，通过回归方法直接预测凝集块中的血小板数量。

## 项目结构

```
.
├── src/
│   ├── models/
│   │   └── efficientnet_regressor.py           # 模型定义
│   ├── data/
│   │   ├── dataset.py               # 数据加载器
│   │   └── generate_synthetic_data.py # 合成数据生成
│   ├── config/
│   │   ├── __init__.py              # 配置模块初始化
│   │   └── config_manager.py        # 配置管理器
│   └── utils/                       # 工具函数
├── datasets/                        # 数据目录
│   ├── train.csv                    # 训练数据
│   ├── val.csv                      # 验证数据
│   ├── test.csv                     # 测试数据
│   └── images/                      # 图像文件
├── checkpoints/                     # 模型保存目录
├── logs/                           # 训练日志
├── results/                        # 结果输出
├── config.yaml                     # 主配置文件
├── train.py                        # 配置化训练脚本
├── evaluate.py                     # 配置化评估脚本
├── example_simple_usage.py         # 简单使用示例
├── requirements.txt                # 项目依赖
└── README.md                       # 项目说明
```

## 安装依赖

### 在线安装
```bash
pip install -r requirements.txt
```

### 离线安装
项目已优化为离线友好，详细的离线安装指南请参考 [OFFLINE_SETUP.md](OFFLINE_SETUP.md)

```bash
# 使用conda（推荐）
conda create -n plt_env python=3.9
conda activate plt_env
conda install pytorch torchvision cpuonly -c pytorch  # CPU版本
# 或
conda install pytorch torchvision pytorch-cuda=11.8 -c pytorch -c nvidia  # GPU版本
conda install pandas numpy pillow scikit-learn matplotlib tqdm pyyaml tensorboard
```

## 配置文件

项目使用YAML配置文件来管理所有参数。主配置文件为`config.yaml`，包含以下配置项：

- **数据配置**: 训练、验证、测试数据路径
- **模型配置**: 模型架构、预训练权重、Dropout等
- **训练配置**: 批次大小、学习率、优化器、损失函数等
- **数据增强配置**: 图像变换参数
- **验证配置**: 评估指标、早停参数
- **输出配置**: 模型保存、日志、结果目录

### 配置使用方式

项目采用简化的函数式接口，使用起来非常简单：

```python
from src.config import load_config

# 加载配置
config = load_config("config.yaml")

# 直接访问配置
data_config = config.get('data', {})
training_config = config.get('training', {})
batch_size = training_config.get('batch_size', 32)
```

## 数据准备

1. 准备CSV文件，包含以下列：
   - image_path: 凝集块图像路径
   - plt_count: 血小板数量

2. 将数据分为训练集、验证集和测试集，分别保存为：
   - datasets/train.csv
   - datasets/val.csv
   - datasets/test.csv

3. 或者使用合成数据生成器：
```bash
python src/data/generate_synthetic_data.py
```

## 训练模型

```bash
python train.py --config config.yaml
```

训练过程中会：
- 自动保存最佳模型到checkpoints目录
- 生成TensorBoard日志
- 支持早停机制
- 保存训练配置副本

## 评估模型

```bash
python evaluate.py --config config.yaml
```

可选参数：
- `--model`: 指定模型文件路径（默认使用最佳模型）

评估结果将包括：
- MSE（均方误差）
- RMSE（均方根误差）
- MAE（平均绝对误差）
- R²分数（决定系数）
- 预测值与真实值对比图
- 误差分布图
- 详细的统计信息

### 评估指标使用方式

项目提供了简单的函数式评估指标接口：

```python
from src.utils.metrics import calculate_metrics, print_metrics, plot_predictions

# 计算指标
metrics = calculate_metrics(predictions, targets)
print(f"MAE: {metrics['mae']:.4f}")

# 打印格式化结果
print_metrics(predictions, targets, "模型评估结果")

# 生成可视化图表
plot_predictions(predictions, targets, "results.png")
```

## 模型特点

1. **灵活的模型架构**: 支持EfficientNetV2-S等多种骨干网络
2. **回归预测**: 直接预测血小板数量，输出连续值
3. **多种损失函数**: 支持MSE、MAE、Huber损失
4. **GPU加速**: 支持CUDA训练，可选混合精度训练
5. **数据增强**: 支持多种图像增强技术（翻转、旋转、亮度/对比度调整，基于torchvision）
6. **早停机制**: 防止过拟合，自动停止训练
7. **TensorBoard集成**: 实时监控训练过程
8. **配置化管理**: 所有参数通过YAML文件统一管理
9. **离线友好**: 基于torchvision，无需额外在线依赖，适合离线环境
10. **简化接口**: 函数式设计，简单易用，无复杂的类结构

## 离线使用特性

✅ **完全离线支持**: 所有功能基于PyTorch内置模块
✅ **无额外依赖**: 移除了albumentations等需要联网的库
✅ **标准数据增强**: 使用torchvision.transforms实现所有增强功能
✅ **预训练权重**: 支持本地预训练权重文件
✅ **配置化管理**: 通过YAML文件管理所有参数

## 配置说明

### 数据增强配置
- `horizontal_flip`: 水平翻转概率
- `vertical_flip`: 垂直翻转概率
- `rotation`: 旋转角度范围
- `brightness`: 亮度调整范围
- `contrast`: 对比度调整范围

### 训练配置
- `batch_size`: 批次大小，建议根据GPU内存调整
- `learning_rate`: 学习率，默认0.001
- `optimizer`: 优化器类型（adam, adamw, sgd）
- `loss_function`: 损失函数（mse, mae, huber）
- `use_amp`: 是否使用混合精度训练

### 验证配置
- `patience`: 早停耐心值，验证损失不改善的最大epoch数
- `save_best`: 是否保存最佳模型

## 使用TensorBoard监控训练

```bash
tensorboard --logdir logs/
```

然后在浏览器中访问 http://localhost:6006 查看训练过程。

## 注意事项

1. **数据格式**: 确保CSV文件包含正确的列名（image_path, plt_count）
2. **图像路径**: 图像路径可以是相对路径或绝对路径
3. **GPU内存**: 根据GPU内存大小调整batch_size，避免内存溢出
4. **数据增强**: 训练时启用数据增强，验证/测试时禁用
5. **模型预测**: 预测结果自动限制为非负值
6. **配置备份**: 训练时会自动保存配置文件副本，便于实验管理

## 实验管理

项目支持完整的实验管理：
- 自动创建时间戳目录
- 保存训练配置副本
- 记录详细的评估指标
- 生成可视化结果图表