#!/usr/bin/env python3
"""
PLT血小板计数模型推理脚本
Inference script for PLT platelet counting model
"""

import os
import torch
import numpy as np
import pandas as pd
import argparse
from datetime import datetime
from PIL import Image
from typing import List, Tuple, Union
import glob

from src.config import load_config
from src.data.transforms import create_inference_transform
from src.utils.model_utils import setup_model_for_inference


class PLTPredictor:
    """PLT血小板计数预测器"""

    def __init__(self, model_path: str = None, config_path: str = 'config/config.yaml', device: str = None):
        """
        初始化预测器

        Args:
            model_path: 模型文件路径，如果为None则使用配置中的最佳模型
            config_path: 配置文件路径
            device: 计算设备，如果为None则使用配置中的设备
        """
        self.config = load_config(config_path)

        # 设置模型和设备
        self.model, self.device = setup_model_for_inference(
            self.config, model_path, device
        )

        # 创建数据变换
        augmentation_config = self.config.get('augmentation', {})
        self.transform = create_inference_transform(augmentation_config)

        print("预测器初始化完成")

    def predict_single(self, image_path: str) -> Tuple[float, dict]:
        """
        预测单张图像

        Args:
            image_path: 图像文件路径

        Returns:
            (预测值, 详细信息字典)
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")

        # 加载和预处理图像
        image = Image.open(image_path).convert('RGB')
        input_tensor = self.transform(image).unsqueeze(0).to(self.device)

        # 预测
        with torch.no_grad():
            raw_prediction = self.model(input_tensor).item()
            rounded_prediction = max(0, round(raw_prediction))  # 确保非负并四舍五入

        info = {
            'image_path': image_path,
            'raw_prediction': raw_prediction,
            'rounded_prediction': rounded_prediction
        }

        return rounded_prediction, info

    def predict_batch(self, image_paths: List[str], batch_size: int = 32) -> List[Tuple[float, dict]]:
        """
        批量预测图像

        Args:
            image_paths: 图像文件路径列表
            batch_size: 批次大小

        Returns:
            预测结果列表，每个元素为(预测值, 详细信息字典)
        """
        results = []

        print(f"开始批量预测，共{len(image_paths)}张图像")

        for i in range(0, len(image_paths), batch_size):
            batch_paths = image_paths[i:i + batch_size]
            batch_images = []
            batch_info = []

            # 加载批次图像
            for image_path in batch_paths:
                if not os.path.exists(image_path):
                    print(f"警告: 图像文件不存在，跳过: {image_path}")
                    continue

                try:
                    image = Image.open(image_path).convert('RGB')
                    input_tensor = self.transform(image)
                    batch_images.append(input_tensor)
                    batch_info.append(image_path)
                except Exception as e:
                    print(f"警告: 无法加载图像 {image_path}: {e}")
                    continue

            if not batch_images:
                continue

            # 批量预测
            batch_tensor = torch.stack(batch_images).to(self.device)

            with torch.no_grad():
                raw_predictions = self.model(batch_tensor).cpu().numpy().flatten()

            # 处理结果
            for j, (raw_pred, image_path) in enumerate(zip(raw_predictions, batch_info)):
                rounded_pred = max(0, round(raw_pred))

                info = {
                    'image_path': image_path,
                    'raw_prediction': float(raw_pred),
                    'rounded_prediction': rounded_pred
                }

                results.append((rounded_pred, info))

            print(f"已完成 {min(i + batch_size, len(image_paths))}/{len(image_paths)} 张图像")

        return results

    def predict_directory(self, directory_path: str, extensions: List[str] = None, batch_size: int = 32) -> List[Tuple[float, dict]]:
        """
        预测目录中的所有图像

        Args:
            directory_path: 图像目录路径
            extensions: 支持的图像扩展名列表
            batch_size: 批次大小

        Returns:
            预测结果列表
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']

        # 查找所有图像文件
        image_paths = []
        for ext in extensions:
            pattern = os.path.join(directory_path, f"*{ext}")
            image_paths.extend(glob.glob(pattern))
            pattern = os.path.join(directory_path, f"*{ext.upper()}")
            image_paths.extend(glob.glob(pattern))

        if not image_paths:
            raise ValueError(f"在目录 {directory_path} 中未找到任何图像文件")

        print(f"在目录 {directory_path} 中找到 {len(image_paths)} 张图像")

        return self.predict_batch(image_paths, batch_size)

    def save_predictions_to_csv(self, results: List[Tuple[float, dict]], output_path: str = None) -> str:
        """
        将预测结果保存到CSV文件

        Args:
            results: predict或predict_batch的返回结果
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if output_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_dir = self.config.get('output', {}).get('results_dir', 'results')
            os.makedirs(results_dir, exist_ok=True)
            output_path = os.path.join(results_dir, f'predictions_{timestamp}.csv')

        # 转换结果为DataFrame
        predictions_data = []
        for pred_value, info in results:
            predictions_data.append({
                'image_path': os.path.basename(info['image_path']),
                'full_path': info['image_path'],
                'raw_prediction': info['raw_prediction'],
                'rounded_prediction': info['rounded_prediction']
            })

        # 创建DataFrame并保存
        df = pd.DataFrame(predictions_data)
        df.to_csv(output_path, index=False)
        print(f"预测结果已保存到: {output_path}")

        return output_path


def main():
    parser = argparse.ArgumentParser(description='PLT血小板计数模型推理')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--model', type=str, default=None,
                       help='模型文件路径（可选，默认使用配置中的最佳模型）')
    parser.add_argument('--image', type=str, required=True,
                       help='要预测的图片路径，可以是单张图片或包含图片的文件夹')
    parser.add_argument('--device', type=str, default=None,
                       help='计算设备 (例如: cuda:0, cpu)')
    parser.add_argument('--output', type=str, default=None,
                       help='预测结果保存路径（CSV格式，可选）')
    parser.add_argument('--batch-size', type=int, default=32,
                       help='批量预测时的批次大小')
    args = parser.parse_args()

    try:
        # 创建预测器
        predictor = PLTPredictor(args.model, args.config, args.device)

        # 判断输入是文件还是目录
        if os.path.isfile(args.image):
            # 单张图像预测
            print(f"预测单张图像: {args.image}")
            prediction, info = predictor.predict_single(args.image)
            print(f"预测结果: {prediction} (原始值: {info['raw_prediction']:.4f})")

            # 保存结果
            result = [(prediction, info)]

        elif os.path.isdir(args.image):
            # 批量预测
            print(f"批量预测目录: {args.image}")
            result = predictor.predict_directory(args.image, batch_size=args.batch_size)

            print(f"\n批量预测完成，共预测 {len(result)} 张图像")
            print("预测结果摘要:")
            predictions = [r[0] for r in result]
            print(f"  平均值: {np.mean(predictions):.2f}")
            print(f"  最小值: {np.min(predictions):.2f}")
            print(f"  最大值: {np.max(predictions):.2f}")
            print(f"  标准差: {np.std(predictions):.2f}")

        else:
            raise ValueError(f"输入路径既不是文件也不是目录: {args.image}")

        # 保存预测结果到CSV
        output_csv = predictor.save_predictions_to_csv(result, args.output)
        print(f"\n所有预测结果已保存到: {output_csv}")

    except Exception as e:
        print(f"预测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()