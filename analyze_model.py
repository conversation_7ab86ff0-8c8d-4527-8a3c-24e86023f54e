#!/usr/bin/env python3
"""
PLT血小板计数模型分析脚本
Model analysis script for PLT platelet counting
"""

import argparse

from src.config import load_config
from src.utils.model_utils import (
    create_model_from_config,
    print_model_info,
    benchmark_model_inference,
    estimate_model_memory,
    print_memory_usage,
    compare_models
)


def analyze_single_model(config_path: str, model_path: str = None, batch_sizes: list = None):
    """
    分析单个模型的性能
    
    Args:
        config_path: 配置文件路径
        model_path: 模型文件路径
        batch_sizes: 要测试的批次大小列表
    """
    if batch_sizes is None:
        batch_sizes = [1, 4, 8, 16, 32]
    
    # 加载配置
    config = load_config(config_path)
    
    # 创建模型
    model = create_model_from_config(config)
    
    # 加载模型权重（如果提供）
    if model_path:
        from src.utils.model_utils import load_model_weights
        import torch
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        load_model_weights(model, model_path, device)
        model = model.to(device)
    
    # 打印模型信息
    print_model_info(model)
    
    # 获取输入形状
    augmentation_config = config.get('augmentation', {})
    input_size = augmentation_config.get('resize', [224, 224])
    input_shape = [3] + input_size  # [C, H, W]
    
    print(f"\n输入形状: {input_shape}")
    
    # 分析不同批次大小的性能
    print("\n" + "="*50)
    print("不同批次大小的性能分析")
    print("="*50)
    
    for batch_size in batch_sizes:
        print(f"\n--- 批次大小: {batch_size} ---")
        
        # 内存使用分析
        memory_stats = estimate_model_memory(model, input_shape, batch_size)
        print_memory_usage(memory_stats, batch_size, input_shape)
        
        # 推理性能分析
        try:
            perf_results = benchmark_model_inference(
                model, input_shape, [batch_size], num_runs=50
            )
            print(f"推理性能: {perf_results[f'batch_{batch_size}']['fps']:.2f} FPS")
        except Exception as e:
            print(f"推理性能测试失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='PLT血小板计数模型性能分析')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--model', type=str, default=None,
                       help='模型文件路径（可选）')
    parser.add_argument('--batch-sizes', type=int, nargs='+', default=[1, 4, 8, 16, 32],
                       help='要测试的批次大小列表')
    parser.add_argument('--compare', action='store_true',
                       help='比较不同模型的性能')
    
    args = parser.parse_args()
    
    try:
        if args.compare:
            config = load_config(args.config)
            compare_models(config)
        else:
            analyze_single_model(args.config, args.model, args.batch_sizes)
            
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
